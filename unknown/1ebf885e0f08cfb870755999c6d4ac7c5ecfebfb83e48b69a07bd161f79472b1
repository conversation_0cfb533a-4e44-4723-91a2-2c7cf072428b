import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>ircle, Star, RefreshCw, Loader2, TrendingUp } from 'lucide-react'
import { cn } from '../../../utils/cn'
import { StatsResponse } from '../../../services/stats/statsService'

interface PerformanceCardProps {
  stats: StatsResponse | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

const PerformanceCard: React.FC<PerformanceCardProps> = ({
  stats,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {
  const completionRate = stats ? Math.round((stats.total_attempted_tasks / Math.max(stats.total_tasks, 1)) * 100) : 0
  const scorePercentage = stats ? Math.round((stats.total_scored / Math.max(stats.total_possible_score, 1)) * 100) : 0

  return (
    <motion.div
      variants={cardVariants}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden shadow-lg"
    >
      {/* Header */}
      <div className="p-3 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="p-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg shadow-sm">
            <TrendingUp className="h-4 w-4 text-white" />
          </div>
          <div>
            <h3 className="text-base font-semibold text-slate-900 dark:text-white">
              Performance
            </h3>
            <p className="text-xs text-slate-500 dark:text-slate-400">
              Your progress metrics
            </p>
          </div>
        </div>
        <button
          onClick={onRefresh}
          disabled={loading}
          className={cn(
            "p-1.5 rounded-lg transition-colors",
            "bg-slate-100 hover:bg-slate-200 dark:bg-slate-800 dark:hover:bg-slate-700",
            "focus:outline-none focus:ring-2 focus:ring-blue-500",
            loading && "opacity-50 cursor-not-allowed"
          )}
        >
          <RefreshCw className={cn("h-3.5 w-3.5 text-slate-600 dark:text-slate-400", loading && "animate-spin")} />
        </button>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="m-3 p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg"
        >
          <p className="text-red-600 dark:text-red-400 text-sm font-medium">{error}</p>
        </motion.div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mx-auto mb-3" />
            <p className="text-slate-600 dark:text-slate-400 text-sm">Loading performance...</p>
          </div>
        </div>
      ) : (
        <div className="p-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Completion Progress */}
            <motion.div
              className="p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 cursor-pointer"
              whileHover={{ scale: 1.02, y: -1 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Completion Rate
                  </span>
                </div>
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-0.5 rounded">
                  {completionRate}%
                </span>
              </div>
              <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden relative">
                <motion.div
                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full shadow-inner relative overflow-hidden"
                  initial={{ width: 0 }}
                  animate={{ width: `${completionRate}%` }}
                  transition={{
                    duration: 1.2,
                    ease: "easeOut",
                    delay: 0.5
                  }}
                >
                  {/* Flowing water effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                    animate={{
                      x: ['-100%', '200%']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear",
                      delay: 1.5
                    }}
                    style={{
                      width: '50%',
                      height: '100%',
                      transform: 'skewX(-20deg)'
                    }}
                  />
                </motion.div>
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1.5">
                {stats?.total_attempted_tasks || 0} of {stats?.total_tasks || 0} tasks completed
              </p>
            </motion.div>

            {/* Score Performance */}
            <motion.div
              className="p-3 rounded-lg border border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800/50 cursor-pointer"
              whileHover={{ scale: 1.02, y: -1 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                    Score Rate
                  </span>
                </div>
                <span className="text-sm font-bold text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/30 px-2 py-0.5 rounded">
                  {scorePercentage}%
                </span>
              </div>
              <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2 overflow-hidden relative">
                <motion.div
                  className="bg-gradient-to-r from-amber-500 to-amber-600 h-2 rounded-full shadow-inner relative overflow-hidden"
                  initial={{ width: 0 }}
                  animate={{ width: `${scorePercentage}%` }}
                  transition={{
                    duration: 1.2,
                    ease: "easeOut",
                    delay: 0.8
                  }}
                >
                  {/* Flowing water effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                    animate={{
                      x: ['-100%', '200%']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear",
                      delay: 2
                    }}
                    style={{
                      width: '50%',
                      height: '100%',
                      transform: 'skewX(-20deg)'
                    }}
                  />
                </motion.div>
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1.5">
                {stats?.total_scored || 0} of {stats?.total_possible_score || 0} possible points
              </p>
            </motion.div>
          </div>
        </div>
      )}
    </motion.div>
  )
}

export default PerformanceCard
