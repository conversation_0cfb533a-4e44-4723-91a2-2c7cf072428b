import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  Calendar, 
  Target, 
  Zap, 
  Trophy, 
  Clock,
  BookOpen,
  Brain,
  Flame,
  Star
} from 'lucide-react'
import { cn } from '../../../utils/cn'

interface LearningInsightsCardProps {
  cardVariants: any
}

interface Insight {
  id: string
  type: 'streak' | 'achievement' | 'progress' | 'milestone'
  title: string
  description: string
  value: string | number
  icon: React.ComponentType<any>
  color: string
  bgColor: string
  isNew?: boolean
}

const LearningInsightsCard: React.FC<LearningInsightsCardProps> = ({
  cardVariants
}) => {
  const [currentTime, setCurrentTime] = useState(new Date())

  // Update time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Mock insights data - in real app, this would come from props or API
  const insights: Insight[] = [
    {
      id: '1',
      type: 'streak',
      title: 'Learning Streak',
      description: 'Days in a row',
      value: 7,
      icon: Flame,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      isNew: false
    },
    {
      id: '2',
      type: 'achievement',
      title: 'Quiz Master',
      description: 'Perfect scores achieved',
      value: 3,
      icon: Trophy,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
      isNew: true
    },
    {
      id: '3',
      type: 'progress',
      title: 'Study Time',
      description: 'Minutes today',
      value: 45,
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      isNew: false
    },
    {
      id: '4',
      type: 'milestone',
      title: 'Vocabulary',
      description: 'Words learned',
      value: 127,
      icon: Brain,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      isNew: false
    }
  ]

  const weeklyGoal = {
    current: 5,
    target: 7,
    percentage: Math.round((5 / 7) * 100)
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <motion.div
      variants={cardVariants}
      whileHover={{ 
        scale: 1.02,
        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
      }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden shadow-lg h-full flex flex-col"
    >
      {/* Header */}
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 shadow-sm">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                Learning Insights
              </h3>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                {formatDate(currentTime)} • {formatTime(currentTime)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 space-y-6">
        {/* Weekly Goal Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Weekly Goal
            </h4>
            <span className="text-xs text-slate-500 dark:text-slate-400">
              {weeklyGoal.current}/{weeklyGoal.target} days
            </span>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-600 dark:text-slate-400">Progress</span>
              <span className="font-semibold text-slate-900 dark:text-white">
                {weeklyGoal.percentage}%
              </span>
            </div>
            <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${weeklyGoal.percentage}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
            </div>
          </div>
        </div>

        {/* Insights Grid */}
        <div className="grid grid-cols-2 gap-3">
          {insights.map((insight, index) => (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              className={cn(
                "relative p-3 rounded-lg border border-slate-200 dark:border-slate-700 hover:shadow-md transition-all duration-200 cursor-pointer group",
                insight.bgColor
              )}
              whileHover={{ scale: 1.05 }}
            >
              {insight.isNew && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
                />
              )}
              
              <div className="flex items-center gap-2 mb-2">
                <insight.icon className={cn("h-4 w-4", insight.color)} />
                <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                  {insight.title}
                </span>
              </div>
              
              <div className="space-y-1">
                <div className={cn("text-lg font-bold", insight.color)}>
                  {insight.value}
                </div>
                <div className="text-xs text-slate-500 dark:text-slate-400">
                  {insight.description}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Quick Actions
          </h4>
          <div className="grid grid-cols-2 gap-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-shadow flex items-center gap-2"
            >
              <BookOpen className="h-4 w-4" />
              Start Quiz
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-shadow flex items-center gap-2"
            >
              <Star className="h-4 w-4" />
              Review
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default LearningInsightsCard
