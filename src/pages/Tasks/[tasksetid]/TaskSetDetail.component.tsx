import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  RefreshCw,
  Headphones,
  Clock,
  ChevronRight,
  AlertCircle,
  FileText,
  RotateCcw,
  ArrowLeft
} from 'lucide-react'
import { Link } from 'react-router-dom'
import MainLayout from '../../../components/layout/MainLayout'
import SimpleAudioPlayer from '../../../components/audio/SimpleAudioPlayer.tsx'
import { TaskSetDetailSkeleton, TaskItemsSkeleton } from '../../../components/ui/Skeleton'
import TaskSetScoreDisplay from '../../../components/ui/TaskSetScoreDisplay'

import { cn } from '../../../utils/cn'

interface TaskSetDetailComponentProps {
  taskSetId: string
  taskSet: any | null
  taskSetScore: any | null
  taskQuestions?: any[] // Array of question data for task cards
  loading: boolean
  loadingScore?: boolean
  loadingQuestions?: boolean
  error: string | null
  onTaskItemClick: (taskItemId: string, taskIndex: number) => void
  onViewTasks: () => void
  onViewStories: () => void
  onResetTask: () => void
  onRefresh: () => void
}

/**
 * TaskSetDetail Component - Pure UI component for task set detail page
 */
const TaskSetDetailComponent: React.FC<TaskSetDetailComponentProps> = ({
  taskSet,
  taskSetScore,
  taskQuestions,
  loading,
  loadingScore = false,
  loadingQuestions = false,
  error,
  onTaskItemClick,
  onViewTasks,
  onViewStories,
  onResetTask,
  onRefresh
}) => {
  const [showRetryConfirmation, setShowRetryConfirmation] = useState(false)
  // Helper function to get display title from input_content
  const getTaskSetTitle = () => {
    if (!taskSet?.input_content) return 'Task Set'

    // If input_content is a string, return it
    if (typeof taskSet.input_content === 'string') {
      return taskSet.input_content
    }

    // If input_content is an object, extract meaningful title
    if (typeof taskSet.input_content === 'object') {
      return taskSet.input_content.file_name ||
             taskSet.input_content.object_name ||
             'Audio Task Set'
    }

    return 'Task Set'
  }

  // Helper function to get audio URL from input_content
  const getAudioUrl = () => {
    if (!taskSet?.input_content || typeof taskSet.input_content !== 'object') {
      return null
    }
    return taskSet.input_content.url || null
  }

  // Helper function to check if this is an audio task set
  const isAudioTaskSet = () => {
    return taskSet?.input_type === 'audio' && getAudioUrl()
  }




  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <MainLayout
      title={taskSet ? getTaskSetTitle() : 'Task Set Details'}
      description="View and complete task items in this set"
    >
      <div className="space-y-6">
        {/* Back Navigation */}
        <div className="flex items-center gap-3">
          <Link 
            to="/tasks" 
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="text-sm">Back to Tasks</span>
          </Link>
        </div>

        {/* Error state */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-destructive/10 border border-destructive/20 rounded-xl p-6"
          >
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <h3 className="font-medium text-destructive">Error</h3>
                <p className="text-sm text-destructive/80">{error}</p>
              </div>
              <button
                onClick={onRefresh}
                className="ml-auto p-2 hover:bg-destructive/10 rounded-lg transition-colors"
              >
                <RefreshCw className="h-4 w-4 text-destructive" />
              </button>
            </div>
          </motion.div>
        )}

        {/* Loading state */}
        {loading ? (
          <div className="space-y-6">
            <TaskSetDetailSkeleton />
            <TaskItemsSkeleton count={3} />
          </div>
        ) : taskSet ? (
          <>
            {/* Task Set Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-card border border-border rounded-xl p-6 shadow-sm"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "p-3 rounded-lg",
                    taskSet.input_type === 'audio'
                      ? "bg-blue-100 dark:bg-blue-900/20"
                      : "bg-purple-100 dark:bg-purple-900/20"
                  )}>
                    {taskSet.input_type === 'audio' ? (
                      <Headphones className={cn(
                        "h-5 w-5",
                        "text-blue-600 dark:text-blue-400"
                      )} />
                    ) : (
                      <FileText className={cn(
                        "h-5 w-5",
                        "text-purple-600 dark:text-purple-400"
                      )} />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Created {formatTimeAgo(taskSet.created_at)}</span>
                      <span>•</span>
                      <span>{taskSet.tasks?.length || 0} task{(taskSet.tasks?.length || 0) !== 1 ? 's' : ''}</span>
                      <span>•</span>
                      <span>Ready to start</span>
                    </div>

                  </div>
                </div>
                <button
                  onClick={onRefresh}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  title="Refresh"
                >
                  <RefreshCw className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>

              {/* Simple Audio Player for audio task sets */}
              {isAudioTaskSet() && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="mb-6"
                >
                  <SimpleAudioPlayer
                    src={getAudioUrl()!}
                    title={getTaskSetTitle()}
                  />
                </motion.div>
              )}

              {/* Task Set Score Display */}
              <TaskSetScoreDisplay
                score={taskSetScore}
                loading={loadingScore}
                className="mb-6"
              />

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={onViewTasks}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2 justify-center"
                >
                  <FileText className="h-4 w-4" />
                  View Tasks
                </button>
                <button
                  onClick={onViewStories}
                  className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2 justify-center"
                >
                  <FileText className="h-4 w-4" />
                  View Stories
                </button>
                <button
                  onClick={() => setShowRetryConfirmation(true)}
                  className="px-6 py-3 bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/40 text-red-700 dark:text-red-300 rounded-lg font-medium transition-colors flex items-center gap-2 justify-center border border-red-200 dark:border-red-800"
                >
                  <RotateCcw className="h-4 w-4" />
                  Retry Task Set
                </button>
              </div>
            </motion.div>

            {/* Task Items List */}
            {!taskSet.tasks || taskSet.tasks.length === 0 ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12 px-6 bg-card border border-border rounded-xl"
              >
                <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-muted mb-4">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-medium mb-2 text-card-foreground">No tasks found</h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  This task set doesn't have any tasks yet.
                </p>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6"
              >
                {taskSet.tasks.map((taskId: string, index: number) => {
                  // Find corresponding question data for this task ID
                  const questionData = taskQuestions?.find(q => q.id === taskId || q._id === taskId)

                  // Extract question text
                  const questionText = questionData?.question?.text || `Task ${index + 1}`

                  return (
                    <motion.div
                      key={taskId}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                      className="cursor-pointer"
                      onClick={() => onTaskItemClick(taskId, index)}
                    >
                      <div className="bg-card border border-border rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-shadow">
                        {/* Header */}
                        <div className="flex items-start justify-between mb-2 sm:mb-3">
                          <div className="flex items-center gap-1.5 sm:gap-2">
                            <div className="w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400">
                              {index + 1}
                            </div>
                            <div className="p-0.5 sm:p-1 rounded-full bg-blue-100 dark:bg-blue-900/20">
                              <Clock className="h-2.5 w-2.5 sm:h-3 sm:w-3 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                          <ChevronRight className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-muted-foreground" />
                        </div>

                        {/* Content */}
                        <h3 className="font-medium text-card-foreground mb-2 line-clamp-2 text-sm sm:text-base">
                          {loadingQuestions ? 'Loading...' : questionText}
                        </h3>

                        <div className="flex items-center justify-between text-xs sm:text-sm">
                          <span className="text-muted-foreground truncate">
                            {questionData?.type?.replace('_', ' ') || taskSet.input_type || 'audio'} task
                          </span>
                          <span className="font-medium text-blue-600 ml-2">
                            {questionData ? `${questionData.scored || 0}/${questionData.total_score || 10}` : '0/10'}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </motion.div>
            )}
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-12 px-6 bg-card border border-border rounded-xl"
          >
            <div className="inline-flex items-center justify-center h-16 w-16 rounded-full bg-muted mb-4">
              <AlertCircle className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-medium mb-2 text-card-foreground">Task set not found</h3>
            <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
              The requested task set could not be found.
            </p>
          </motion.div>
        )}
      </div>

      {/* Retry Confirmation Modal */}
      {showRetryConfirmation && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-slate-800 rounded-xl p-6 max-w-md w-full border border-border shadow-xl"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-full">
                <RotateCcw className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <h3 className="text-lg font-semibold text-foreground">Retry Task Set</h3>
            </div>

            <p className="text-muted-foreground mb-6">
              This will delete all progress and scores in this task set. You'll start fresh from the beginning. This action cannot be undone.
            </p>

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowRetryConfirmation(false)}
                className="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowRetryConfirmation(false)
                  onResetTask()
                }}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
              >
                Yes, Retry Task Set
              </button>
            </div>
          </motion.div>
        </div>
      )}

    </MainLayout>
  )
}

export default TaskSetDetailComponent
