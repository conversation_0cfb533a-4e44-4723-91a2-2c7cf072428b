import React, { useEffect, useRef } from 'react'
import { <PERSON>, useNavi<PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  Volume2,
  BookOpen,
  Award,
  Play,
  ArrowRight,
  Sparkles,
  Headphones,
  Mic
} from 'lucide-react'
import SVGAnimatedCharacter from '../../components/ui/SVGAnimatedCharacter'
import AnimatedCharacter from '../../components/ui/AnimatedCharacter'

/**
 * Animated Home Page with sound waves and beautiful animations
 */
const Home: React.FC = () => {
  const soundWaveRef = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()

  const handleLoginClick = () => {
    // Direct navigation without any loading screen
    navigate('/login')
  }

  useEffect(() => {
    // All animations are now handled by framer-motion
    // This useEffect can be removed or used for other initialization
  }, [])

  const generateSoundWaves = () => {
    return Array.from({ length: 30 }, (_, i) => (
      <motion.div
        key={i}
        className="bg-gradient-to-t from-blue-400 via-purple-500 to-pink-400 rounded-full mx-0.5 shadow-lg"
        style={{
          width: `${3 + Math.random() * 4}px`,
        }}
        animate={{
          height: [
            `${15 + Math.random() * 20}px`,
            `${40 + Math.random() * 80}px`,
            `${15 + Math.random() * 20}px`
          ],
          opacity: [0.6, 1, 0.6],
          scaleY: [0.8, 1.2, 0.8]
        }}
        transition={{
          duration: 1.5 + Math.random() * 2,
          repeat: Infinity,
          delay: i * 0.05,
          ease: "easeInOut"
        }}
      />
    ))
  }

  const generateFloatingElements = () => {
    return Array.from({ length: 15 }, (_, i) => (
      <motion.div
        key={i}
        className="absolute"
        style={{
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
        }}
        animate={{
          y: [0, -30, 0],
          x: [0, Math.random() * 20 - 10, 0],
          rotate: [0, 360],
          opacity: [0.3, 0.8, 0.3]
        }}
        transition={{
          duration: 8 + Math.random() * 4,
          repeat: Infinity,
          delay: Math.random() * 5,
          ease: "easeInOut"
        }}
      >
        {i % 3 === 0 ? (
          <Sparkles className="h-4 w-4 text-yellow-400" />
        ) : i % 3 === 1 ? (
          <div className="w-2 h-2 bg-blue-400 rounded-full shadow-lg" />
        ) : (
          <div className="w-1 h-1 bg-purple-400 rounded-full shadow-lg" />
        )}
      </motion.div>
    ))
  }

  return (
    <div className="h-screen bg-background overflow-hidden">
      {/* Enhanced Background Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Elements */}
        {generateFloatingElements()}

        {/* Enhanced Particles */}
        {Array.from({ length: 20 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full opacity-30"
            style={{
              width: `${2 + Math.random() * 4}px`,
              height: `${2 + Math.random() * 4}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              background: i % 3 === 0 ?
                'linear-gradient(45deg, #3b82f6, #8b5cf6)' :
                i % 3 === 1 ?
                'linear-gradient(45deg, #8b5cf6, #ec4899)' :
                'linear-gradient(45deg, #06b6d4, #3b82f6)'
            }}
            animate={{
              x: [0, Math.random() * 100 - 50],
              y: [0, Math.random() * 100 - 50],
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.5, 0.5]
            }}
            transition={{
              duration: Math.random() * 4 + 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}

        {/* Shooting Stars */}
        {Array.from({ length: 3 }, (_, i) => (
          <motion.div
            key={`star-${i}`}
            className="absolute"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 50}%`,
            }}
            animate={{
              x: [0, 300],
              y: [0, 150],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 5 + Math.random() * 3,
              ease: "easeOut"
            }}
          >
            <div className="w-1 h-1 bg-yellow-400 rounded-full shadow-lg">
              <div className="absolute inset-0 bg-yellow-400 rounded-full animate-ping" />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Floating Characters */}
      <div className="absolute top-20 left-10">
        <AnimatedCharacter
          size="sm"
          character="wizard"
          mood="thinking"
          className="opacity-60"
        />
      </div>
      <div className="absolute top-40 right-20">
        <AnimatedCharacter
          size="sm"
          character="scientist"
          mood="focused"
          className="opacity-50"
        />
      </div>
      <div className="absolute bottom-40 left-20">
        <AnimatedCharacter
          size="sm"
          character="artist"
          mood="happy"
          className="opacity-70"
        />
      </div>



      <div className="relative z-10 flex h-screen">
        {/* Main Content - Responsive width */}
        <div className="lg:w-[90%] flex-1 flex flex-col justify-center items-center px-4 py-4 transition-all duration-500 overflow-hidden">
          {/* Hero Section */}
          <div className="text-center max-w-4xl mx-auto">
            {/* Logo and Title */}
            <motion.div
              className="mb-4"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, type: "spring" }}
            >
              <div className="flex items-center justify-center gap-3 mb-4">
                <SVGAnimatedCharacter
                  size={80}
                  mood="excited"
                  className="animate-pulse"
                />
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl">
                  <Volume2 className="h-10 w-10 text-white" />
                </div>
                <AnimatedCharacter
                  size="md"
                  character="hero"
                  mood="celebrating"
                />
              </div>
              <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-3">
                Audio Quiz Genie
              </h1>
              <p className="text-xl text-muted-foreground mb-2">
                Learn Nepali Through Interactive Audio Quizzes
              </p>
              <p className="text-base text-muted-foreground/80">
                Master pronunciation, vocabulary, and comprehension with AI-powered feedback
              </p>
            </motion.div>

            {/* Enhanced Sound Wave Visualization */}
            <div className="mb-6">
              <div className="relative bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-pink-900/20 dark:from-blue-800/30 dark:via-purple-800/30 dark:to-pink-800/30 rounded-2xl p-6 backdrop-blur-sm border border-border shadow-2xl">
                <div ref={soundWaveRef} className="flex items-end justify-center h-24 mb-3 gap-1">
                  {generateSoundWaves()}
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent rounded-2xl pointer-events-none" />
                <motion.div
                  className="text-center"
                  animate={{ opacity: [0.7, 1, 0.7] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  <p className="text-sm text-muted-foreground font-medium">
                    🎵 Interactive Audio Learning Experience
                  </p>
                  <p className="text-xs text-muted-foreground/80 mt-1">
                    Real-time audio visualization
                  </p>
                </motion.div>
              </div>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-3 gap-4 mb-6">
              <motion.div
                className="bg-card/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-border group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Headphones className="h-6 w-6 text-blue-600 group-hover:text-blue-700" />
                </motion.div>
                <h3 className="text-lg font-semibold mb-2 text-foreground group-hover:text-blue-600 transition-colors">Audio Learning</h3>
                <p className="text-muted-foreground">
                  Listen, learn, and practice with native pronunciation guides
                </p>
              </motion.div>

              <motion.div
                className="bg-card/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-border group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Mic className="h-6 w-6 text-purple-600 group-hover:text-purple-700" />
                </motion.div>
                <h3 className="text-lg font-semibold mb-2 text-foreground group-hover:text-purple-600 transition-colors">Speech Recognition</h3>
                <p className="text-muted-foreground">
                  AI-powered feedback on your pronunciation and speaking skills
                </p>
              </motion.div>

              <motion.div
                className="bg-card/80 backdrop-blur-sm rounded-2xl p-4 shadow-xl border border-border group"
                whileHover={{
                  scale: 1.08,
                  y: -10,
                  boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="w-12 h-12 bg-pink-100 dark:bg-pink-900/30 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300"
                >
                  <Award className="h-6 w-6 text-pink-600 group-hover:text-pink-700" />
                </motion.div>
                <h3 className="text-lg font-semibold mb-2 text-foreground group-hover:text-pink-600 transition-colors">Progress Tracking</h3>
                <p className="text-muted-foreground">
                  Track your learning journey with detailed analytics
                </p>
              </motion.div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
            >
              <Link to="/signup">
                <motion.button
                  className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl font-semibold text-base shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center gap-3 group"
                  whileHover={{
                    scale: 1.08,
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    y: -3
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <Play className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  Start Learning Now
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </motion.button>
              </Link>

              <Link to="/dashboard">
                <motion.button
                  className="px-8 py-4 bg-card/80 backdrop-blur-sm text-card-foreground rounded-2xl font-semibold text-base shadow-xl hover:shadow-2xl transition-all duration-300 border border-border group"
                  whileHover={{
                    scale: 1.08,
                    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                    y: -3
                  }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <BookOpen className="h-5 w-5 inline mr-3 group-hover:scale-110 transition-transform" />
                  Explore Dashboard
                </motion.button>
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Login Preview - Always 10% width, enhanced transparent overlay */}
        <div className="lg:w-[10%] relative flex items-center justify-center transition-all duration-500 overflow-hidden">
          {/* Enhanced transparent overlay with better background */}
          <div className="absolute inset-0 bg-gradient-to-l from-card/30 via-card/15 to-transparent backdrop-blur-md border-l border-border shadow-inner" />

          {/* Subtle glow effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-l from-blue-500/5 via-purple-500/5 to-transparent"
            animate={{
              opacity: [0.3, 0.6, 0.3]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            whileHover={{
              scale: 1.05,
              backgroundColor: "rgba(255, 255, 255, 0.1)"
            }}
            whileTap={{ scale: 0.95 }}
            className="relative z-10 cursor-pointer h-full w-full flex items-center justify-center p-2 rounded-l-lg transition-all duration-300"
            onClick={handleLoginClick}
          >
            {/* Horizontal layout for larger screens */}
            <div className="hidden lg:flex items-center justify-center w-full">
              <div className="flex flex-col items-center space-y-2">
                <motion.div
                  className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Sparkles className="h-4 w-4 text-white" />
                </motion.div>

                <div className="text-center">
                  <p className="text-xs font-semibold text-foreground">
                    Sign In
                  </p>
                  <motion.div
                    animate={{
                      x: [0, 6, 0],
                      scale: [1, 1.2, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="text-blue-600 dark:text-blue-400 flex justify-center mt-1"
                  >
                    <ArrowRight className="h-5 w-5" />
                  </motion.div>
                </div>
              </div>
            </div>

            {/* Mobile layout */}
            <div className="lg:hidden flex items-center space-x-2">
              <motion.div
                className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Sparkles className="h-3 w-3 text-white" />
              </motion.div>
              <p className="text-xs font-semibold text-foreground">
                Sign In
              </p>
              <motion.div
                animate={{
                  x: [0, 6, 0],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="text-blue-600 dark:text-blue-400"
              >
                <ArrowRight className="h-4 w-4" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

export default Home
