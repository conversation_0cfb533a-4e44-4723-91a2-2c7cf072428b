import React from 'react'
import { GoogleLogin } from '@react-oauth/google'
import { motion } from 'framer-motion'
import { GoogleAuthComponentProps } from '../../types'

/**
 * GoogleAuth Component - Google OAuth sign-in button component
 */
const GoogleAuthComponent: React.FC<GoogleAuthComponentProps> = ({
  onSuccess,
  onError,
  disabled = false,
}) => {
  const handleSuccess = (credentialResponse: any) => {
    if (credentialResponse.credential) {
      onSuccess(credentialResponse.credential)
    } else {
      onError()
    }
  }

  const handleError = () => {
    console.error('Google Sign-In failed')
    onError()
  }

  return (
    <div className="w-full">
      {/* Custom styled wrapper for Google button */}
      <motion.div
        whileHover={{
          scale: 1.02,
          y: -2,
          boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
        }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 300, damping: 20 }}
        className="relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
      >
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-white to-red-50/50 dark:from-blue-950/20 dark:via-card dark:to-red-950/20" />

        {/* Border and content */}
        <div className="relative bg-card/95 backdrop-blur-sm border border-border rounded-2xl overflow-hidden">
          <div className="google-login-wrapper">
            <GoogleLogin
              onSuccess={handleSuccess}
              onError={handleError}
              disabled={disabled}
              theme="outline"
              size="large"
              width="100%"
              text="signin_with"
              shape="rectangular"
            />
          </div>
        </div>
      </motion.div>

      {/* Custom CSS to style the Google button */}
      <style jsx>{`
        .google-login-wrapper :global(div[role="button"]) {
          border-radius: 1rem !important;
          border: none !important;
          background: transparent !important;
          box-shadow: none !important;
          font-family: inherit !important;
          font-weight: 500 !important;
          padding: 12px 16px !important;
        }

        .google-login-wrapper :global(div[role="button"]:hover) {
          background: rgba(0, 0, 0, 0.02) !important;
        }

        .dark .google-login-wrapper :global(div[role="button"]:hover) {
          background: rgba(255, 255, 255, 0.05) !important;
        }
      `}</style>
    </div>
  )
}

export default GoogleAuthComponent
